<template>
	<view class="container">
		<!-- MRG首页 -->
		<view class="mrg-home">
			<!-- 顶部标题 -->
			<view class="header-section">
				<view class="welcome-text">WELCOME TO</view>
				<view class="mrg-title">MRG</view>
			</view>
			
			<!-- 中间图表区域 -->
			<view class="chart-section">
				<view class="chart-container">
					<!-- 加密货币图标和标签 -->
					<view class="crypto-item usdt-item">
						<view class="crypto-icon usdt-icon">₮</view>
						<view class="crypto-label">USDT</view>
					</view>
					
					<view class="crypto-item btc-item">
						<view class="crypto-icon btc-icon">₿</view>
						<view class="crypto-label">BTC</view>
					</view>
					
					<view class="crypto-item usdt-item-right">
						<view class="crypto-icon usdt-icon">₮</view>
						<view class="crypto-label">USDT</view>
					</view>
					
					<view class="crypto-item etc-item">
						<view class="crypto-icon etc-icon">Ξ</view>
						<view class="crypto-label">ETC</view>
					</view>
					
					<!-- 中心美元符号 -->
					<view class="center-symbol">$</view>
					
					<!-- 连接线和图表背景 -->
					<view class="chart-background"></view>
				</view>
			</view>
			
			<!-- 底部按钮区域 -->
			<view class="button-section">
				<view class="mrg-button primary-button" @click="goToEarn">
					MRG赚币
				</view>
				<view class="mrg-button secondary-button" @click="logout">
					退出
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 简化的数据结构
		}
	},
	onLoad() {
		// 页面加载时的初始化
		console.log('MRG首页加载');
	},
	onShow() {
		// 页面显示时的逻辑
		console.log('MRG首页显示');
	},
	mounted() {
		// 组件挂载时的初始化
		if (!sessionStorage.getItem('lang')) {
			sessionStorage.setItem('lang', 'cn')
		}
		console.log('MRG首页组件挂载完成');
	},
	methods: {
		// MRG赚币按钮点击事件
		goToEarn() {
			uni.showToast({
				title: '进入MRG赚币',
				icon: 'success'
			});
			// 这里可以跳转到赚币页面
			// uni.navigateTo({ url: '/pages/earn/earn' })
		},
		
		// 退出按钮点击事件
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出应用吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除登录信息
						sessionStorage.removeItem("token");
						sessionStorage.removeItem("address");
						sessionStorage.removeItem("userInfo");
						uni.showToast({
							title: '已退出',
							icon: 'success'
						});
						// 可以跳转到登录页面或重新加载
						// location.reload();
					}
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
}

.mrg-home {
	width: 100%;
	max-width: 750rpx;
	height: 100vh;
	background: linear-gradient(180deg, #e8f4f8 0%, #f0f8fa 100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	padding: 80rpx 40rpx 120rpx;
	box-sizing: border-box;
}

/* 顶部标题区域 */
.header-section {
	text-align: center;
	margin-top: 60rpx;
}

.welcome-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 20rpx;
	font-weight: 400;
}

.mrg-title {
	font-size: 120rpx;
	font-weight: bold;
	color: #00bcd4;
	letter-spacing: 8rpx;
}

/* 中间图表区域 */
.chart-section {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.chart-container {
	position: relative;
	width: 500rpx;
	height: 500rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.chart-background {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 300rpx;
	height: 300rpx;
	background: linear-gradient(45deg, rgba(0, 188, 212, 0.1) 0%, rgba(0, 188, 212, 0.05) 100%);
	border-radius: 50%;
	border: 2rpx solid rgba(0, 188, 212, 0.2);
}

.center-symbol {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	font-size: 80rpx;
	color: #00bcd4;
	font-weight: bold;
	z-index: 10;
}

/* 加密货币图标定位 */
.crypto-item {
	position: absolute;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 5;
}

.usdt-item {
	top: 20rpx;
	left: 50%;
	transform: translateX(-50%);
}

.btc-item {
	top: 50%;
	left: 20rpx;
	transform: translateY(-50%);
}

.usdt-item-right {
	top: 50%;
	right: 20rpx;
	transform: translateY(-50%);
}

.etc-item {
	bottom: 20rpx;
	left: 50%;
	transform: translateX(-50%);
}

.crypto-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-bottom: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-weight: bold;
	font-size: 24rpx;
}

.usdt-icon {
	background: #26a17b;
}

.btc-icon {
	background: #f7931a;
}

.etc-icon {
	background: #328332;
}

.crypto-label {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

/* 底部按钮区域 */
.button-section {
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.mrg-button {
	width: 100%;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36rpx;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.primary-button {
	background: #00bcd4;
	color: white;
	border: none;
}

.primary-button:active {
	background: #00acc1;
	transform: scale(0.98);
}

.secondary-button {
	background: transparent;
	color: #00bcd4;
	border: 2rpx solid #00bcd4;
}

.secondary-button:active {
	background: rgba(0, 188, 212, 0.1);
	transform: scale(0.98);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.mrg-home {
		padding: 60rpx 30rpx 100rpx;
	}
	
	.mrg-title {
		font-size: 100rpx;
	}
	
	.chart-container {
		width: 400rpx;
		height: 400rpx;
	}
	
	.crypto-icon {
		width: 60rpx;
		height: 60rpx;
		font-size: 20rpx;
	}
	
	.center-symbol {
		font-size: 60rpx;
	}
}
</style>
