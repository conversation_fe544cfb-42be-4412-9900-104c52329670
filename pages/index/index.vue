<template>
	<view class="container">
		<!-- MRG首页 -->
		<view class="mrg-home">
			<!-- 顶部标题 -->
			<view class="header-section">
				<view class="welcome-text">WELCOME TO</view>
				<view class="mrg-title">MRG</view>
			</view>
			
			<!-- 中间图表区域 -->
			<view class="chart-section">
				<view class="chart-container">
					<!-- 加密货币图标和标签 -->
					<view class="crypto-item usdt-item">
						<view class="crypto-icon usdt-icon">₮</view>
						<view class="crypto-label">USDT</view>
					</view>
					
					<view class="crypto-item btc-item">
						<view class="crypto-icon btc-icon">₿</view>
						<view class="crypto-label">BTC</view>
					</view>
					
					<view class="crypto-item usdt-item-right">
						<view class="crypto-icon usdt-icon">₮</view>
						<view class="crypto-label">USDT</view>
					</view>
					
					<view class="crypto-item etc-item">
						<view class="crypto-icon etc-icon">Ξ</view>
						<view class="crypto-label">ETC</view>
					</view>
					
					<!-- 中心美元符号 -->
					<view class="center-symbol">$</view>
					
					<!-- 连接线和图表背景 -->
					<view class="chart-background"></view>
				</view>
			</view>
			
			<!-- 底部按钮区域 -->
			<view class="button-section">
				<view class="mrg-button primary-button" @click="goToEarn">
					MRG赚币
				</view>
				<view class="mrg-button secondary-button" @click="logout">
					退出
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 简化的数据结构
		}
	},
	onLoad() {
		// 页面加载时的初始化
		console.log('MRG首页加载');
	},
	onShow() {
		// 页面显示时的逻辑
		console.log('MRG首页显示');
	},
	mounted() {
		// 组件挂载时的初始化
		if (!sessionStorage.getItem('lang')) {
			sessionStorage.setItem('lang', 'cn')
		}
		console.log('MRG首页组件挂载完成');
	},
	methods: {
		// MRG赚币按钮点击事件
		goToEarn() {
			uni.showToast({
				title: '进入MRG赚币',
				icon: 'success'
			});
			// 这里可以跳转到赚币页面
			// uni.navigateTo({ url: '/pages/earn/earn' })
		},
		
		// 退出按钮点击事件
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出应用吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除登录信息
						sessionStorage.removeItem("token");
						sessionStorage.removeItem("address");
						sessionStorage.removeItem("userInfo");
						uni.showToast({
							title: '已退出',
							icon: 'success'
						});
						// 可以跳转到登录页面或重新加载
						// location.reload();
					}
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	background: linear-gradient(180deg, #E8F4F8 0%, #F0F8FA 100%);
	min-height: 100vh;
	width: 100%;
}

.mrg-home {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	padding: 120rpx 60rpx 80rpx;
	box-sizing: border-box;
}

/* 顶部标题区域 */
.header-section {
	text-align: center;
	margin-top: 0;
}

.welcome-text {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	font-size: 28rpx;
	color: #8E8E93;
	margin-bottom: 16rpx;
	font-weight: 400;
	letter-spacing: 1rpx;
}

.mrg-title {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	font-size: 140rpx;
	font-weight: 700;
	color: #00BCD4;
	letter-spacing: 12rpx;
	margin: 0;
}

/* 中间图表区域 */
.chart-section {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	margin: 60rpx 0;
}

.chart-container {
	position: relative;
	width: 480rpx;
	height: 480rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.chart-background {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 280rpx;
	height: 280rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	border: 1rpx solid rgba(0, 188, 212, 0.15);
	backdrop-filter: blur(10rpx);
}

.center-symbol {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	font-size: 100rpx;
	color: #00BCD4;
	font-weight: 300;
	z-index: 10;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 加密货币图标定位 */
.crypto-item {
	position: absolute;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 15;
}

.usdt-item {
	top: 30rpx;
	left: 50%;
	transform: translateX(-50%);
}

.btc-item {
	top: 50%;
	left: 30rpx;
	transform: translateY(-50%);
}

.usdt-item-right {
	top: 50%;
	right: 30rpx;
	transform: translateY(-50%);
}

.etc-item {
	bottom: 30rpx;
	left: 50%;
	transform: translateX(-50%);
}

.crypto-icon {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	margin-bottom: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-weight: 600;
	font-size: 28rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.usdt-icon {
	background: linear-gradient(135deg, #26A17B 0%, #1E8A66 100%);
}

.btc-icon {
	background: linear-gradient(135deg, #F7931A 0%, #E8820E 100%);
}

.etc-icon {
	background: linear-gradient(135deg, #328332 0%, #2A6F2A 100%);
}

.crypto-label {
	font-size: 22rpx;
	color: #8E8E93;
	font-weight: 500;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 底部按钮区域 */
.button-section {
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 24rpx;
	padding: 0 20rpx;
}

.mrg-button {
	width: 100%;
	height: 96rpx;
	border-radius: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	letter-spacing: 1rpx;
}

.primary-button {
	background: #00BCD4;
	color: white;
	border: none;
	box-shadow: 0 4rpx 16rpx rgba(0, 188, 212, 0.3);
}

.primary-button:active {
	background: #00ACC1;
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 188, 212, 0.4);
}

.secondary-button {
	background: rgba(255, 255, 255, 0.4);
	color: #00BCD4;
	border: 2rpx solid #00BCD4;
	backdrop-filter: blur(10rpx);
}

.secondary-button:active {
	background: rgba(0, 188, 212, 0.1);
	transform: scale(0.98);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.mrg-home {
		padding: 60rpx 30rpx 100rpx;
	}
	
	.mrg-title {
		font-size: 100rpx;
	}
	
	.chart-container {
		width: 400rpx;
		height: 400rpx;
	}
	
	.crypto-icon {
		width: 60rpx;
		height: 60rpx;
		font-size: 20rpx;
	}
	
	.center-symbol {
		font-size: 60rpx;
	}
}
</style>
